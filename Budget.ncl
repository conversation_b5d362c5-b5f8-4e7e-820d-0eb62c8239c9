
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"

begin
  ;--- File path
  file_path = "/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc"

  ;--- Check if file exists
  if (.not. fileexists(file_path)) then
    print("ERROR: File does not exist: " + file_path)
    exit
  end if

  ;--- Open ERA5 file
  print("Opening ERA5 file: " + file_path)
  f = addfile(file_path,"r")

  ;--- Check and read variables with error handling
  required_vars = (/"tisr", "tsr", "ttr", "ssr", "str", "sshf", "slhf", "latitude", "longitude", "valid_time"/)

  do i = 0, dimsizes(required_vars)-1
    if (.not. isfilevar(f, required_vars(i))) then
      print("ERROR: Variable " + required_vars(i) + " not found in file")
      exit
    end if
  end do

  print("Reading variables...")
  tisr = f->tisr   ; TOA incident SW radiation (W/m²)
  tsr  = f->tsr    ; TOA net SW radiation (W/m²)
  ttr  = f->ttr    ; TOA net LW radiation (W/m²)

  ssr  = f->ssr    ; Surface net SW radiation (W/m²)
  str  = f->str    ; Surface net LW radiation (W/m²)

  sshf = f->sshf   ; Surface sensible heat flux (W/m²)
  slhf = f->slhf   ; Surface latent heat flux (W/m²)

  lat = f->latitude
  lon = f->longitude
  time = f->valid_time

  ;--- Print variable info
  print("Data dimensions:")
  print("  Time: " + dimsizes(time))
  print("  Latitude: " + dimsizes(lat))
  print("  Longitude: " + dimsizes(lon))

  ;--- Check for missing values
  print("Checking for missing values...")
  if (any(ismissing(tisr))) then
    print("WARNING: Missing values found in TISR")
  end if
  if (any(ismissing(tsr))) then
    print("WARNING: Missing values found in TSR")
  end if
  if (any(ismissing(ttr))) then
    print("WARNING: Missing values found in TTR")
  end if

  ;--- CRITICAL: Convert from accumulated energy (J/m²) to flux rates (W/m²)
  ;--- ERA5 monthly data: need to convert J/m² to W/m² by dividing by seconds in month
  ;--- For monthly data, approximate: 1 month ≈ 30.44 days × 24 hours × 3600 seconds = 2,629,746 seconds
  seconds_per_month = 30.44 * 24.0 * 3600.0  ; Average seconds per month

  print("Converting accumulated energy (J/m²) to flux rates (W/m²)...")
  print("Using conversion factor: " + seconds_per_month + " seconds per month")

  ;--- Convert all variables from J/m² to W/m²
  tisr_flux = tisr / seconds_per_month  ; TOA incident SW flux
  tsr_flux = tsr / seconds_per_month    ; TOA net SW flux
  ttr_flux = ttr / seconds_per_month    ; TOA net LW flux
  ssr_flux = ssr / seconds_per_month    ; Surface net SW flux
  str_flux = str / seconds_per_month    ; Surface net LW flux
  sshf_flux = sshf / seconds_per_month  ; Surface sensible heat flux
  slhf_flux = slhf / seconds_per_month  ; Surface latent heat flux

  ;--- CORRECTED: Compute TOA net radiation (downward positive)
  ;--- Based on CF standard names:
  ;--- tsr: "toa_net_upward_shortwave_flux" - upward positive, so negate for downward positive
  ;--- ttr: "toa_outgoing_longwave_flux" - outgoing (upward), so negate for downward positive
  Rtoa = -tsr_flux - ttr_flux  ; TOA net radiation (downward positive)
  copy_VarMeta(tsr, Rtoa)
  Rtoa@long_name = "TOA net radiation (downward positive)"
  Rtoa@units = "W m-2"
  Rtoa@description = "TOA net radiation = -(net upward SW) - (outgoing LW)"

  ;--- Compute surface net radiation (downward positive)
  ;--- ssr: "surface_net_downward_shortwave_flux" - already downward positive
  ;--- str: "surface_net_upward_longwave_flux" - upward positive, so negate for downward positive
  Rsfc = ssr_flux - str_flux  ; Surface net radiation (downward positive)
  copy_VarMeta(ssr, Rsfc)
  Rsfc@long_name = "Surface net radiation (downward positive)"
  Rsfc@units = "W m-2"
  Rsfc@description = "Surface net radiation = net downward SW - net upward LW"

  ;--- Energy budget residual
  ;--- Energy balance: Rtoa = Rsfc + H + LE + dE/dt
  ;--- sshf: "surface_upward_sensible_heat_flux" - upward positive
  ;--- slhf: "surface_upward_latent_heat_flux" - upward positive
  ;--- Residual = Rtoa - Rsfc - H - LE (should be close to zero for energy balance)
  residual = Rtoa - Rsfc - sshf_flux - slhf_flux
  copy_VarMeta(tsr, residual)
  residual@long_name = "Energy budget residual"
  residual@units = "W m-2"
  residual@description = "Rtoa - Rsfc - H - LE (should be ~0 for energy balance)"

  ;--- Calculate area weights for proper global averaging
  rad = 4.0*atan(1.0)/180.0  ; Convert degrees to radians
  clat = cos(lat*rad)        ; Cosine of latitude for area weighting
  Rtoa_global = wgt_areaave_Wrap(Rtoa, clat, 1.0, 1)
  Rsfc_global = wgt_areaave_Wrap(Rsfc, clat, 1.0, 1)
  sshf_global = wgt_areaave_Wrap(sshf_flux, clat, 1.0, 1)
  slhf_global = wgt_areaave_Wrap(slhf_flux, clat, 1.0, 1)
  residual_global = wgt_areaave_Wrap(residual, clat, 1.0, 1)

  ;--- Calculate time means
  Rtoa_mean = dim_avg_n_Wrap(Rtoa_global, 0)
  Rsfc_mean = dim_avg_n_Wrap(Rsfc_global, 0)
  sshf_mean = dim_avg_n_Wrap(sshf_global, 0)
  slhf_mean = dim_avg_n_Wrap(slhf_global, 0)
  residual_mean = dim_avg_n_Wrap(residual_global, 0)

  ;--- Print results
  print("")
  print("=== ERA5 ENERGY BUDGET ANALYSIS ===")
  print("Time-averaged global means:")
  print("  TOA net radiation:     " + sprintf("%6.2f", Rtoa_mean) + " W/m²")
  print("  Surface net radiation: " + sprintf("%6.2f", Rsfc_mean) + " W/m²")
  print("  Sensible heat flux:    " + sprintf("%6.2f", sshf_mean) + " W/m²")
  print("  Latent heat flux:      " + sprintf("%6.2f", slhf_mean) + " W/m²")
  print("  Energy budget residual:" + sprintf("%6.2f", residual_mean) + " W/m²")
  print("")
  print("Energy balance check:")
  print("  Rtoa - Rsfc - H - LE = " + sprintf("%6.2f", residual_mean) + " W/m²")
  print("  (Should be close to 0 for perfect energy balance)")

  ;--- Calculate standard deviations
  Rtoa_std = dim_stddev_n_Wrap(Rtoa_global, 0)
  residual_std = dim_stddev_n_Wrap(residual_global, 0)
  print("")
  print("Temporal variability (standard deviation):")
  print("  TOA net radiation:     " + sprintf("%6.2f", Rtoa_std) + " W/m²")
  print("  Energy budget residual:" + sprintf("%6.2f", residual_std) + " W/m²")

  ;--- CORRECTED: Create time series plots
  print("")
  print("Creating plots...")

  wks = gsn_open_wks("pdf","ERA5_energy_budget")

  ;--- Convert time to years for better plotting
  time_years = cd_calendar(time, 0)  ; Convert to calendar format
  years = time_years(:,0) + (time_years(:,1)-1)/12.0  ; Convert to decimal years

  ;--- Plot 1: Energy budget components time series
  res = True
  res@gsnMaximize = True
  res@tiMainString = "ERA5 Global Mean Energy Budget Components (1990-2020)"
  res@tiXAxisString = "Year"
  res@tiYAxisString = "Energy Flux (W/m²)"
  res@xyLineThicknesses = (/2.0, 2.0, 2.0, 2.0/)
  res@xyLineColors = (/"red", "blue", "green", "orange"/)
  res@xyDashPatterns = (/0, 0, 0, 0/)
  res@pmLegendDisplayMode = "Always"
  res@pmLegendSide = "Top"
  res@pmLegendParallelPosF = 0.8
  res@pmLegendOrthogonalPosF = -0.4
  res@pmLegendWidthF = 0.15
  res@pmLegendHeightF = 0.1
  res@lgLabelFontHeightF = 0.015
  res@xyExplicitLegendLabels = (/"TOA net", "Surface net", "Sensible", "Latent"/)

  data = new((/4, dimsizes(time)/), float)
  data(0,:) = (/Rtoa_global/)
  data(1,:) = (/Rsfc_global/)
  data(2,:) = (/sshf_global/)
  data(3,:) = (/slhf_global/)

  plot1 = gsn_csm_xy(wks, years, data, res)

  ;--- Plot 2: Energy budget residual
  delete(res@xyLineColors)
  delete(res@xyExplicitLegendLabels)
  delete(res@pmLegendDisplayMode)
  res@tiMainString = "ERA5 Energy Budget Residual (1990-2020)"
  res@xyLineColors = "black"
  res@xyLineThicknesses = 2.0

  plot2 = gsn_csm_xy(wks, years, residual_global, res)

  print("Plots saved to: ERA5_energy_budget.pdf")

end
