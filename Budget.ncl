load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"
begin
  ;--- Open ERA5 file
  f = addfile("/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc","r")

  ;--- Read variables
  tisr = f->tisr   ; TOA incident SW
  tsr  = f->tsr    ; TOA net SW
  ttr  = f->ttr    ; TOA net LW

  ssr  = f->ssr    ; surface net SW
  str  = f->str    ; surface net LW

  sshf = f->sshf   ; sensible heat flux
  slhf = f->slhf   ; latent heat flux

  lat = f->latitude
  lon = f->longitude

  ;--- Compute TOA net radiation (downward positive)
  Rtoa = tisr - tsr - ttr  ; TOA net = incoming SW - net SW - net LW

  ;--- Compute surface net radiation
  Rsfc = ssr + str          ; surface net = net SW + net LW

  ;--- Energy budget residual
  residual = Rtoa - Rsfc + sshf + slhf

  ;--- Global mean
  Rtoa_global = dim_avg_n_Wrap(Rtoa,1)
  Rtoa_global = dim_avg_n_Wrap(Rtoa_global,1)

  Rsfc_global = dim_avg_n_Wrap(Rsfc,1)
  Rsfc_global = dim_avg_n_Wrap(Rsfc_global,1)

  residual_global = dim_avg_n_Wrap(residual,1)
  residual_global = dim_avg_n_Wrap(residual_global,1)

  print("Global mean TOA net radiation:")
  print(Rtoa_global)
  print("Global mean Surface net radiation:")
  print(Rsfc_global)
  print("Global mean residual:")
  print(residual_global)

  ;--- Optional: time series plot
  wks = gsn_open_wks("pdf","ERA5_energy_budget")
  res = True
  res@tiMainString = "ERA5 Energy Budget Residual"
  plot = gsn_csm_xy(wks, residual_global, res)

end