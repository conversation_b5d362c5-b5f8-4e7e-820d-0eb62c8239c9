;===============================================================
;  NCL script: ERA5 Energy Budget (1990–2020)
;===============================================================
begin
  ;-- Input file
  f = addfile("/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc","r")

  ;-- Read variables (all in J/m2, accumulated over month)
  tisr  = f->tisr    ; TOA incident shortwave [J/m2]
  tsr   = f->tsr     ; TOA net upward shortwave [J/m2] (CF: toa_net_upward_shortwave_flux)
  ttr   = f->ttr     ; TOA outgoing longwave [J/m2] (CF: toa_outgoing_longwave_flux)
  ssr   = f->ssr     ; surface net downward shortwave [J/m2]
  str   = f->str     ; surface net upward longwave [J/m2] (CF: surface_net_upward_longwave_flux)
  sshf  = f->sshf    ; surface upward sensible heat flux [J/m2]
  slhf  = f->slhf    ; surface upward latent heat flux [J/m2]

  ;-- Convert to W/m2 (ERA5 accumulations are in J/m2 per month)
  vtime = f->valid_time

  ; Convert to calendar components to get days in each month
  time_components = cd_calendar(vtime, 0)   ; (year, mon, day, hour, min, sec)
  years = toint(time_components(:,0))
  months = toint(time_components(:,1))

  ; Calculate actual days in each month
  days_in_month = days_in_month(months, years)
  sec_in_month = days_in_month * 24 * 3600

  print("Time range: " + years(0) + "-" + years(dimsizes(years)-1))
  print("Days per month range: " + min(days_in_month) + " to " + max(days_in_month))

  ; Convert from J/m2 to W/m2 using actual seconds in each month
  tisr  = tisr  / conform(tisr, sec_in_month, 0)
  tsr   = tsr   / conform(tsr, sec_in_month, 0)
  ttr   = ttr   / conform(ttr, sec_in_month, 0)
  ssr   = ssr   / conform(ssr, sec_in_month, 0)
  str   = str   / conform(str, sec_in_month, 0)
  sshf  = sshf  / conform(sshf, sec_in_month, 0)
  slhf  = slhf  / conform(slhf, sec_in_month, 0)

  ;-- Compute energy budget terms (downward positive convention)
  ; TOA net radiation = incident SW - net upward SW - outgoing LW
  R_toa = tisr - tsr - ttr

  ; Surface net radiation = net downward SW - net upward LW
  R_sfc = ssr - str

  ; Energy budget residual: R_toa - R_sfc - H - LE (should be ~0)
  ; Note: sshf and slhf are upward positive, so subtract them
  residual = R_toa - R_sfc - sshf - slhf

  ;-- Calculate global area-weighted means
  lat = f->latitude
  wgt = cos(lat*0.01745329252)    ; cosine latitude weights

  ; Global area-weighted averages
  R_toa_global = wgt_areaave(R_toa, wgt, 1.0, 0)
  R_sfc_global = wgt_areaave(R_sfc, wgt, 1.0, 0)
  SH_global    = wgt_areaave(sshf, wgt, 1.0, 0)
  LH_global    = wgt_areaave(slhf, wgt, 1.0, 0)
  RES_global   = wgt_areaave(residual, wgt, 1.0, 0)

  ;-- Calculate time means and print results
  print("=== ERA5 Global Energy Budget Analysis ===")
  print("Time-averaged global means:")
  print("  TOA Net Radiation:     " + sprintf("%8.2f", avg(R_toa_global)) + " W/m²")
  print("  Surface Net Radiation: " + sprintf("%8.2f", avg(R_sfc_global)) + " W/m²")
  print("  Sensible Heat Flux:    " + sprintf("%8.2f", avg(SH_global)) + " W/m²")
  print("  Latent Heat Flux:      " + sprintf("%8.2f", avg(LH_global)) + " W/m²")
  print("  Energy Budget Residual:" + sprintf("%8.2f", avg(RES_global)) + " W/m²")
  print("")
  print("Energy balance check:")
  print("  R_toa - R_sfc - H - LE = " + sprintf("%8.2f", avg(RES_global)) + " W/m²")
  print("  (Should be close to 0 for perfect energy balance)")

end
