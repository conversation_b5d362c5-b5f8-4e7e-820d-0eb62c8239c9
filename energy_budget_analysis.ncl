;===============================================================
;  NCL script: ERA5 Energy Budget (1990–2020)
;===============================================================
begin
  ;-- Input file
  f = addfile("/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc","r")

  ;-- Read variables
  tisr  = f->tisr    ; TOA incident solar [J/m2]
  tsr   = f->tsr     ; TOA reflected solar [J/m2]
  ttr   = f->ttr     ; TOA outgoing longwave [J/m2]
  ssr   = f->ssr     ; surface net shortwave [J/m2]
  str   = f->str     ; surface net longwave [J/m2]
  sshf  = f->sshf    ; sensible heat flux [J/m2]
  slhf  = f->slhf    ; latent heat flux [J/m2]

  ;-- Convert to W/m2 (ERA5 accumulations are in J/m2 per month)
  time    = f->valid_time
   ; Convert to calendar dates
  time = cd_calendar(vtime, 0, "day", 1970, 1, 1)   ; 0 = return yyyy,mm,dd,hh,mm,ss
  ntime   = dimsizes(time)
  year    = cd_calendar(time, 0)          ; get YYYYMMDD
  month   = tointeger(year(:,1))          ; month number

  ; seconds per month (for each time step)
  sec_in_month = ut_cal_season_time(time, "seconds")  ; helper in newer NCL
  ; If unavailable, you can hardcode using an array of month lengths * 86400

  tisr  = tisr  / sec_in_month
  tsr   = tsr   / sec_in_month
  ttr   = ttr   / sec_in_month
  ssr   = ssr   / sec_in_month
  str   = str   / sec_in_month
  sshf  = sshf  / sec_in_month
  slhf  = slhf  / sec_in_month

  ;-- Compute terms
  R_toa = tisr - tsr - ttr
  R_sfc = ssr - str
  residual = R_toa - R_sfc + sshf + slhf

  ;-- Select tropics 30S–30N
  lat = f->latitude
  wgt = cos(lat*0.01745329252)    ; weights
  ;tropics = ind(lat.ge.-30 .and. lat.le.30)

  R_toa_trop = wgt_areaave(R_toa, wgt, 1.0, 0)
  R_sfc_trop = wgt_areaave(R_sfc, wgt, 1.0, 0)
  SH_trop    = wgt_areaave(sshf, wgt, 1.0, 0)
  LH_trop    = wgt_areaave(slhf, wgt, 1.0, 0)
  RES_trop   = wgt_areaave(residual, wgt, 1.0, 0)

  ;-- Print means
  print("=== Global Energy Budget ===")
  print("TOA Net Radiation    (W/m2): " + avg(R_toa_trop))
  print("Surface Net Radiation (W/m2): " + avg(R_sfc_trop))
  print("Sensible Heat Flux    (W/m2): " + avg(SH_trop))
  print("Latent Heat Flux      (W/m2): " + avg(LH_trop))
  print("Residual (storage/imbalance) (W/m2): " + avg(RES_trop))

end
