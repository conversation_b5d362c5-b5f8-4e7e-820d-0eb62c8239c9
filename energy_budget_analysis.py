#!/usr/bin/env python3
"""
ERA5 Energy Budget Analysis - Python Version
Corrected version of the NCL script with proper unit conversions and sign conventions
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def main():
    # File path
    file_path = "/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc"
    
    print("Opening ERA5 file:", file_path)
    
    try:
        # Open dataset
        ds = xr.open_dataset(file_path)
        print("Successfully opened dataset")
        
        # Check available variables
        print("\nAvailable variables:")
        for var in ds.data_vars:
            print(f"  {var}: {ds[var].long_name if 'long_name' in ds[var].attrs else 'No description'}")
            print(f"    Units: {ds[var].units if 'units' in ds[var].attrs else 'No units'}")
        
        # Read variables
        print("\nReading energy budget variables...")
        tisr_all = ds['tisr']  # TOA incident SW radiation (J/m²)
        tsr_all = ds['tsr']    # TOA net SW radiation (J/m²)
        ttr_all = ds['ttr']    # TOA net LW radiation (J/m²)
        ssr_all = ds['ssr']    # Surface net SW radiation (J/m²)
        str_all = ds['str']    # Surface net LW radiation (J/m²)
        sshf_all = ds['sshf']  # Surface sensible heat flux (J/m²)
        slhf_all = ds['slhf']  # Surface latent heat flux (J/m²)

        lat = ds['latitude']
        lon = ds['longitude']
        time_all = ds['valid_time']

        # Take only last 30 years (1991-2020): skip first 12 months (1990)
        time_start = 12  # Start from index 12 (January 1991)
        time_end = len(time_all)  # End at last time step (December 2020)

        print(f"Selecting last 30 years (1991-2020)...")
        print(f"  Original time dimension: {len(time_all)}")
        print(f"  Selected time range: {time_start} to {time_end-1}")
        print(f"  Selected time dimension: {time_end - time_start}")

        # Extract 30-year subset for all variables
        tisr = tisr_all[time_start:time_end]
        tsr = tsr_all[time_start:time_end]
        ttr = ttr_all[time_start:time_end]
        ssr = ssr_all[time_start:time_end]
        str_var = str_all[time_start:time_end]
        sshf = sshf_all[time_start:time_end]
        slhf = slhf_all[time_start:time_end]
        time = time_all[time_start:time_end]

        print(f"\nData dimensions (30-year subset):")
        print(f"  Time: {len(time)}")
        print(f"  Latitude: {len(lat)}")
        print(f"  Longitude: {len(lon)}")
        
        # CRITICAL: Convert from accumulated energy (J/m²) to flux rates (W/m²)
        # ERA5 monthly data: need to convert J/m² to W/m² by dividing by seconds in month
        seconds_per_month = 30.44 * 24.0 * 3600.0  # Average seconds per month
        
        print(f"\nConverting accumulated energy (J/m²) to flux rates (W/m²)...")
        print(f"Using conversion factor: {seconds_per_month:.0f} seconds per month")
        
        # Convert all variables from J/m² to W/m²
        tisr_flux = tisr / seconds_per_month
        tsr_flux = tsr / seconds_per_month    
        ttr_flux = ttr / seconds_per_month    
        ssr_flux = ssr / seconds_per_month    
        str_flux = str_var / seconds_per_month    
        sshf_flux = sshf / seconds_per_month  
        slhf_flux = slhf / seconds_per_month  
        
        # CORRECTED: Compute TOA net radiation (downward positive)
        # Based on CF standard names:
        # tsr: "toa_net_upward_shortwave_flux" - upward positive, so negate for downward positive
        # ttr: "toa_outgoing_longwave_flux" - outgoing (upward), so negate for downward positive
        Rtoa = -tsr_flux - ttr_flux  # TOA net radiation (downward positive)
        
        # Compute surface net radiation (downward positive)
        # ssr: "surface_net_downward_shortwave_flux" - already downward positive
        # str: "surface_net_upward_longwave_flux" - upward positive, so negate for downward positive  
        Rsfc = ssr_flux - str_flux  # Surface net radiation (downward positive)
        
        # Energy budget residual
        # Energy balance: Rtoa = Rsfc + H + LE + dE/dt
        # sshf: "surface_upward_sensible_heat_flux" - upward positive
        # slhf: "surface_upward_latent_heat_flux" - upward positive
        # Residual = Rtoa - Rsfc - H - LE (should be close to zero for energy balance)
        residual = Rtoa - Rsfc - sshf_flux - slhf_flux
        
        # Calculate area weights for proper global averaging
        lat_rad = np.deg2rad(lat)
        clat = np.cos(lat_rad)
        
        # Area-weighted global means
        print("\nCalculating area-weighted global means...")
        
        def area_weighted_mean(data, weights):
            """Calculate area-weighted global mean"""
            # Expand weights to match data dimensions
            weights_expanded = weights.broadcast_like(data)
            weighted_data = data * weights_expanded
            return weighted_data.sum(dim=['latitude', 'longitude']) / weights_expanded.sum(dim=['latitude', 'longitude'])
        
        Rtoa_global = area_weighted_mean(Rtoa, clat)
        Rsfc_global = area_weighted_mean(Rsfc, clat)
        sshf_global = area_weighted_mean(sshf_flux, clat)
        slhf_global = area_weighted_mean(slhf_flux, clat)
        residual_global = area_weighted_mean(residual, clat)

        # Calculate time means
        Rtoa_mean = Rtoa_global.mean().values
        Rsfc_mean = Rsfc_global.mean().values
        sshf_mean = sshf_global.mean().values
        slhf_mean = slhf_global.mean().values
        residual_mean = residual_global.mean().values
        print(sshf_mean)

        # Print results
        print("\n" + "="*50)
        print("ERA5 ENERGY BUDGET ANALYSIS")
        print("="*50)
        print("Time-averaged global means:")
        print(f"  TOA net radiation:     {Rtoa_mean:8.2f} W/m²")
        print(f"  Surface net radiation: {Rsfc_mean:8.2f} W/m²")
        print(f"  Sensible heat flux:    {sshf_mean:8.2f} W/m²")
        print(f"  Latent heat flux:      {slhf_mean:8.2f} W/m²")
        print(f"  Energy budget residual:{residual_mean:8.2f} W/m²")
        print()
        print("Energy balance check:")
        print(f"  Rtoa - Rsfc - H - LE = {residual_mean:8.2f} W/m²")
        print("  (Should be close to 0 for perfect energy balance)")
        
        # Calculate standard deviations
        Rtoa_std = Rtoa_global.std().values
        residual_std = residual_global.std().values
        print()
        print("Temporal variability (standard deviation):")
        print(f"  TOA net radiation:     {Rtoa_std:8.2f} W/m²")
        print(f"  Energy budget residual:{residual_std:8.2f} W/m²")
        
        # Create plots
        print("\nCreating plots...")
        
        # Convert time to years for plotting (1991-2020, 30 years)
        years = np.arange(1991, 1991 + len(time)/12, 1/12)[:len(time)]
        
        # Plot 1: Energy budget components
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        ax1.plot(years, Rtoa_global, 'r-', linewidth=2, label='TOA net')
        ax1.plot(years, Rsfc_global, 'b-', linewidth=2, label='Surface net')
        ax1.plot(years, sshf_global, 'g-', linewidth=2, label='Sensible')
        ax1.plot(years, slhf_global, 'orange', linewidth=2, label='Latent')
        ax1.set_xlabel('Year')
        ax1.set_ylabel('Energy Flux (W/m²)')
        ax1.set_title('ERA5 Global Mean Energy Budget Components (1991-2020)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Energy budget residual
        ax2.plot(years, residual_global, 'k-', linewidth=2)
        ax2.set_xlabel('Year')
        ax2.set_ylabel('Energy Flux (W/m²)')
        ax2.set_title('ERA5 Energy Budget Residual (1991-2020)')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig('ERA5_energy_budget.png', dpi=300, bbox_inches='tight')
        print("Plot saved to: ERA5_energy_budget.png")
        
        # Summary assessment
        print("\n" + "="*50)
        print("ENERGY BUDGET ASSESSMENT")
        print("="*50)
        
        if abs(residual_mean) < 5.0:
            print("✓ GOOD: Energy budget residual is small (<5 W/m²)")
        elif abs(residual_mean) < 20.0:
            print("⚠ WARNING: Energy budget residual is moderate (5-20 W/m²)")
        else:
            print("✗ PROBLEM: Energy budget residual is large (>20 W/m²)")
            
        print(f"\nTypical values for comparison:")
        print(f"  TOA net radiation: ~0-2 W/m² (small imbalance)")
        print(f"  Surface net radiation: ~100-120 W/m²")
        print(f"  Sensible heat flux: ~15-25 W/m²")
        print(f"  Latent heat flux: ~80-90 W/m²")
        
        ds.close()
        
    except FileNotFoundError:
        print(f"ERROR: File not found: {file_path}")
    except Exception as e:
        print(f"ERROR: {str(e)}")

if __name__ == "__main__":
    main()
