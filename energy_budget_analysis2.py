#!/usr/bin/env python3
"""
ERA5 Energy Budget Analysis - Python Version
Corrected version of the NCL script with proper unit conversions and sign conventions
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

import xarray as xr

# Open file
file_path = "/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc"
ds = xr.open_dataset(file_path)

# Restrict time to last 30 years (1991-2020)
ds = ds.sel(valid_time=slice("1991-01-01", "2020-12-31"))
print(f"Selected time period: {ds.valid_time.dt.year.min().values}-{ds.valid_time.dt.year.max().values}")
print(f"Number of months: {len(ds.valid_time)}")

# get seconds per month
days_in_month = ds.valid_time.dt.days_in_month
seconds_in_month = days_in_month * 24 * 3600

# convert J/m2 → W/m2
ds_flux = ds / seconds_in_month

# Extract variables with CORRECT sign conventions based on CF standard names
ssr  = ds_flux["ssr"]    # surface net shortwave (downward positive)
str_var = ds_flux["str"] # surface net longwave (upward positive in CF)
tsr  = ds_flux["tsr"]    # TOA net shortwave (upward positive in CF)
ttr  = ds_flux["ttr"]    # TOA outgoing longwave (upward positive in CF)
sshf = ds_flux["sshf"]   # sensible heat flux (upward positive)
slhf = ds_flux["slhf"]   # latent heat flux (upward positive)
tisr = ds_flux["tisr"]   # TOA incident shortwave (downward positive)

# CORRECTED: Net surface radiative flux (downward positive)
# ssr: already downward positive
# str: upward positive, so negate for downward positive
R_sfc = ssr - str_var

# CORRECTED: Net TOA radiative flux (downward positive)
# tsr: upward positive, so negate for downward positive
# ttr: upward positive, so negate for downward positive
R_toa = -tsr - ttr

# Energy budget residual (should be close to zero)
# Energy balance: R_toa = R_sfc + H + LE + dE/dt
# Residual = R_toa - R_sfc - H - LE
residual = R_toa - R_sfc - sshf - slhf

# Global mean (weight by cosine latitude)
weights = np.cos(np.deg2rad(ds["latitude"]))
weights.name = "weights"

R_toa_gmean = R_toa.weighted(weights).mean(("longitude", "latitude"))
R_sfc_gmean = R_sfc.weighted(weights).mean(("longitude", "latitude"))
sshf_gmean = sshf.weighted(weights).mean(("longitude", "latitude"))
slhf_gmean = slhf.weighted(weights).mean(("longitude", "latitude"))
residual_gmean = residual.weighted(weights).mean(("longitude", "latitude"))

# Calculate time means
R_toa_mean = float(R_toa_gmean.mean().values)
R_sfc_mean = float(R_sfc_gmean.mean().values)
sshf_mean = float(sshf_gmean.mean().values)
slhf_mean = float(slhf_gmean.mean().values)
residual_mean = float(residual_gmean.mean().values)

print("="*50)
print("ERA5 ENERGY BUDGET ANALYSIS (1991-2020)")
print("="*50)
print("Time-averaged global means:")
print(f"  TOA net radiation:     {R_toa_mean:8.2f} W/m²")
print(f"  Surface net radiation: {R_sfc_mean:8.2f} W/m²")
print(f"  Sensible heat flux:    {sshf_mean:8.2f} W/m²")
print(f"  Latent heat flux:      {slhf_mean:8.2f} W/m²")
print(f"  Energy budget residual:{residual_mean:8.2f} W/m²")
print()
print("Energy balance check:")
print(f"  R_toa - R_sfc - H - LE = {residual_mean:8.2f} W/m²")
print("  (Should be close to 0 for perfect energy balance)")

# Assessment
if abs(residual_mean) < 5.0:
    print("\n✓ GOOD: Energy budget residual is small (<5 W/m²)")
elif abs(residual_mean) < 20.0:
    print("\n⚠ WARNING: Energy budget residual is moderate (5-20 W/m²)")
else:
    print("\n✗ PROBLEM: Energy budget residual is large (>20 W/m²)")
