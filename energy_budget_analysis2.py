#!/usr/bin/env python3
"""
ERA5 Energy Budget Analysis - Python Version
Corrected version of the NCL script with proper unit conversions and sign conventions
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

import xarray as xr

# Open file
file_path = "/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc"
ds = xr.open_dataset(file_path)

# Restrict time to 1990-2020
ds = ds.sel(valid_time=slice("1990-01-01", "2020-12-31"))
# get seconds per month
days_in_month = ds.valid_time.dt.days_in_month
seconds_in_month = days_in_month * 24 * 3600

# convert J/m2 → W/m2
ds_flux = ds / seconds_in_month

# Extract variables
ssr  = ds_flux["ssr"]    # surface net shortwave, positive downward
strr = ds_flux["str"]    # surface net longwave, positive downward
tsr  = ds_flux["tsr"]    # TOA net shortwave, positive downward
ttr  = ds_flux["ttr"]    # TOA net longwave, positive downward
sshf = ds_flux["sshf"]   # sensible heat flux, positive upward
slhf = ds_flux["slhf"]   # latent heat flux, positive upward
tisr = ds_flux["tisr"]   # TOA incident shortwave, positive downward
# 1. Net surface radiative flux
R_sfc = ssr - strr

# 2. Surface energy balance
SEB = R_sfc - (sshf + slhf)

# 3. Net TOA radiative flux
R_toa = tisr- tsr - ttr

# 4. Atmospheric energy budget
Q_atm = R_toa - SEB

# Global mean (weight by cosine latitude)
weights = np.cos(np.deg2rad(ds["latitude"]))
weights.name = "weights"

Q_atm_gmean = Q_atm.weighted(weights).mean(("longitude", "latitude"))
SEB_gmean   = SEB.weighted(weights).mean(("longitude", "latitude"))
R_toa_gmean = R_toa.weighted(weights).mean(("longitude", "latitude"))

print("Global mean TOA flux (1990–2020):", float(R_toa_gmean.mean().values))
print("Global mean Surface EB (1990–2020):", float(SEB_gmean.mean().values))
print("Global mean Atmospheric EB (1990–2020):", float(Q_atm_gmean.mean().values))
