;===============================================================
;  NCL script: ERA5 Energy Budget (1990–2020) - Simplified Version
;===============================================================
begin
  ;-- Input file
  f = addfile("/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc","r")

  ;-- Read variables (all in J/m2, accumulated over month)
  tisr  = f->tisr    ; TOA incident shortwave [J/m2]
  tsr   = f->tsr     ; TOA net upward shortwave [J/m2] (CF: toa_net_upward_shortwave_flux)
  ttr   = f->ttr     ; TOA outgoing longwave [J/m2] (CF: toa_outgoing_longwave_flux)
  ssr   = f->ssr     ; surface net downward shortwave [J/m2]
  str   = f->str     ; surface net upward longwave [J/m2] (CF: surface_net_upward_longwave_flux)
  sshf  = f->sshf    ; surface upward sensible heat flux [J/m2]
  slhf  = f->slhf    ; surface upward latent heat flux [J/m2]

  ;-- Convert to W/m2 (ERA5 accumulations are in J/m2 per month)
  ; Use average seconds per month for simplicity
  sec_per_month = 30.44 * 24 * 3600  ; Average seconds per month
  
  print("Converting J/m2 to W/m2 using " + sec_per_month + " seconds per month")
  
  ; Convert from J/m2 to W/m2
  tisr  = tisr  / sec_per_month
  tsr   = tsr   / sec_per_month
  ttr   = ttr   / sec_per_month
  ssr   = ssr   / sec_per_month
  str   = str   / sec_per_month
  sshf  = sshf  / sec_per_month
  slhf  = slhf  / sec_per_month

  ;-- Compute energy budget terms (downward positive convention)
  ; TOA net radiation = incident SW - net upward SW - outgoing LW
  R_toa = tisr - tsr - ttr
  
  ; Surface net radiation = net downward SW - net upward LW  
  R_sfc = ssr - str
  
  ; Energy budget residual: R_toa - R_sfc - H - LE (should be ~0)
  ; Note: sshf and slhf are upward positive, so subtract them
  residual = R_toa - R_sfc - sshf - slhf

  ;-- Calculate global area-weighted means
  lat = f->latitude
  wgt = cos(lat*0.01745329252)    ; cosine latitude weights
  
  ; Global area-weighted averages
  R_toa_global = wgt_areaave(R_toa, wgt, 1.0, 0)
  R_sfc_global = wgt_areaave(R_sfc, wgt, 1.0, 0)
  SH_global    = wgt_areaave(sshf, wgt, 1.0, 0)
  LH_global    = wgt_areaave(slhf, wgt, 1.0, 0)
  RES_global   = wgt_areaave(residual, wgt, 1.0, 0)

  ;-- Calculate time means and print results
  print("")
  print("=== ERA5 Global Energy Budget Analysis ===")
  print("Time-averaged global means:")
  print("  TOA Net Radiation:     " + avg(R_toa_global) + " W/m2")
  print("  Surface Net Radiation: " + avg(R_sfc_global) + " W/m2")
  print("  Sensible Heat Flux:    " + avg(SH_global) + " W/m2")
  print("  Latent Heat Flux:      " + avg(LH_global) + " W/m2")
  print("  Energy Budget Residual:" + avg(RES_global) + " W/m2")
  print("")
  print("Energy balance check:")
  print("  R_toa - R_sfc - H - LE = " + avg(RES_global) + " W/m2")
  print("  (Should be close to 0 for perfect energy balance)")
  
  ;-- Assessment
  residual_mean = avg(RES_global)
  if (abs(residual_mean) .lt. 5.0) then
    print("")
    print("GOOD: Energy budget residual is small (<5 W/m2)")
  else if (abs(residual_mean) .lt. 20.0) then
    print("")
    print("WARNING: Energy budget residual is moderate (5-20 W/m2)")
  else
    print("")
    print("PROBLEM: Energy budget residual is large (>20 W/m2)")
  end if
  end if

end
