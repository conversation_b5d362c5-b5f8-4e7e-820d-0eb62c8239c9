#!/usr/bin/env python3
"""
ERA5 Energy Budget Analysis - Tropical Region (30°S to 30°N)
Analysis of energy budget for the tropical belt
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

import xarray as xr

# Open file
file_path = "/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc"
ds = xr.open_dataset(file_path)

# Restrict time to last 30 years (1991-2020)
ds = ds.sel(valid_time=slice("1991-01-01", "2020-12-31"))

# Restrict to tropical region (30°S to 30°N)
ds_tropics = ds.sel(latitude=slice(30, -30))  # Note: ERA5 latitude is decreasing (90 to -90)

print(f"Selected time period: {ds_tropics.valid_time.dt.year.min().values}-{ds_tropics.valid_time.dt.year.max().values}")
print(f"Number of months: {len(ds_tropics.valid_time)}")
print(f"Latitude range: {ds_tropics.latitude.max().values:.1f}°N to {ds_tropics.latitude.min().values:.1f}°S")
print(f"Number of latitude points: {len(ds_tropics.latitude)}")

# get seconds per month - use actual days in each month for accuracy
days_in_month = ds_tropics.valid_time.dt.days_in_month
seconds_in_month = days_in_month * 24 * 3600

print(f"Using actual days per month (range: {days_in_month.min().values}-{days_in_month.max().values} days)")

# convert J/m2 → W/m2
ds_flux = ds_tropics / seconds_in_month

# Extract variables with correct sign conventions based on metadata
ssr  = ds_flux["ssr"]    # surface net shortwave (downward positive)
str_var = ds_flux["str"] # surface net longwave (upward positive in CF)
tsr  = ds_flux["tsr"]    # TOA net shortwave (upward positive in CF)
ttr  = ds_flux["ttr"]    # TOA outgoing longwave (upward positive in CF)
sshf = ds_flux["sshf"]   # sensible heat flux (upward positive)
slhf = ds_flux["slhf"]   # latent heat flux (upward positive)
tisr = ds_flux["tisr"]   # TOA incident shortwave (downward positive)

# Net surface radiative flux (downward positive)
# ssr: already downward positive
# str: upward positive, so negate for downward positive
R_sfc = ssr - str_var

# Net TOA radiative flux (downward positive)
# Based on metadata analysis: tisr - tsr - ttr is correct
R_toa = tisr - tsr - ttr

# Energy budget residual (should be close to zero)
# Energy balance: R_toa = R_sfc + H + LE + dE/dt
# Using the corrected sign convention from user's edit
residual = R_toa - R_sfc + sshf + slhf

# Global mean for tropics (weight by cosine latitude)
weights = np.cos(np.deg2rad(ds_tropics["latitude"]))
weights.name = "weights"

R_toa_tmean = R_toa.weighted(weights).mean(("longitude", "latitude"))
R_sfc_tmean = R_sfc.weighted(weights).mean(("longitude", "latitude"))
sshf_tmean = sshf.weighted(weights).mean(("longitude", "latitude"))
slhf_tmean = slhf.weighted(weights).mean(("longitude", "latitude"))
residual_tmean = residual.weighted(weights).mean(("longitude", "latitude"))

# Calculate time means
R_toa_mean = float(R_toa_tmean.mean().values)
R_sfc_mean = float(R_sfc_tmean.mean().values)
sshf_mean = float(sshf_tmean.mean().values)
slhf_mean = float(slhf_tmean.mean().values)
residual_mean = float(residual_tmean.mean().values)
print(sshf_mean)
print("="*60)
print("ERA5 ENERGY BUDGET ANALYSIS - TROPICS (30°S-30°N, 1991-2020)")
print("="*60)
print("Time-averaged tropical means:")
print(f"  TOA net radiation:     {R_toa_mean:8.2f} W/m²")
print(f"  Surface net radiation: {R_sfc_mean:8.2f} W/m²")
print(f"  Sensible heat flux:    {sshf_mean:8.2f} W/m²")
print(f"  Latent heat flux:      {slhf_mean:8.2f} W/m²")
print(f"  Energy budget residual:{residual_mean:8.2f} W/m²")
print()
print("Energy balance check:")
print(f"  R_toa - R_sfc + H + LE = {residual_mean:8.2f} W/m²")
print("  (Should be close to 0 for perfect energy balance)")

# Assessment
if abs(residual_mean) < 5.0:
    print("\n✓ GOOD: Energy budget residual is small (<5 W/m²)")
elif abs(residual_mean) < 20.0:
    print("\n⚠ WARNING: Energy budget residual is moderate (5-20 W/m²)")
else:
    print("\n✗ PROBLEM: Energy budget residual is large (>20 W/m²)")

# Calculate standard deviations for temporal variability
R_toa_std = float(R_toa_tmean.std().values)
residual_std = float(residual_tmean.std().values)
print()
print("Temporal variability (standard deviation):")
print(f"  TOA net radiation:     {R_toa_std:8.2f} W/m²")
print(f"  Energy budget residual:{residual_std:8.2f} W/m²")

# Create plots
print("\nCreating tropical energy budget plots...")

# Convert time to years for plotting
years = np.arange(1991, 1991 + len(ds_tropics.valid_time)/12, 1/12)[:len(ds_tropics.valid_time)]

# Plot: Energy budget components and residual
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# Plot 1: Energy budget components
ax1.plot(years, R_toa_tmean, 'r-', linewidth=2, label='TOA net')
ax1.plot(years, R_sfc_tmean, 'b-', linewidth=2, label='Surface net')
ax1.plot(years, sshf_tmean, 'g-', linewidth=2, label='Sensible')
ax1.plot(years, slhf_tmean, 'orange', linewidth=2, label='Latent')
ax1.set_xlabel('Year')
ax1.set_ylabel('Energy Flux (W/m²)')
ax1.set_title('ERA5 Tropical Energy Budget Components (30°S-30°N, 1991-2020)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Energy budget residual
ax2.plot(years, residual_tmean, 'k-', linewidth=2)
ax2.set_xlabel('Year')
ax2.set_ylabel('Energy Flux (W/m²)')
ax2.set_title('ERA5 Tropical Energy Budget Residual (30°S-30°N, 1991-2020)')
ax2.grid(True, alpha=0.3)
ax2.axhline(y=0, color='r', linestyle='--', alpha=0.5)

plt.tight_layout()
plt.savefig('ERA5_energy_budget_tropics.png', dpi=300, bbox_inches='tight')
print("Plot saved to: ERA5_energy_budget_tropics.png")

print("\n" + "="*60)
print("TROPICAL vs GLOBAL COMPARISON NOTES")
print("="*60)
print("Expected differences in tropics:")
print("  - Higher TOA net radiation (more solar input)")
print("  - Higher latent heat flux (more evaporation)")
print("  - Lower sensible heat flux (more humid conditions)")
print("  - Different seasonal patterns")
print("\nRun the global script to compare with these tropical results.")

# Save tropical time series for potential comparison
tropical_data = {
    'years': years,
    'R_toa': R_toa_tmean.values,
    'R_sfc': R_sfc_tmean.values,
    'sshf': sshf_tmean.values,
    'slhf': slhf_tmean.values,
    'residual': residual_tmean.values
}

import pickle
with open('tropical_energy_budget_data.pkl', 'wb') as f:
    pickle.dump(tropical_data, f)
print("Tropical data saved to: tropical_energy_budget_data.pkl")
