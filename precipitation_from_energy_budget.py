#!/usr/bin/env python3
"""
ERA5 Precipitation from Energy Budget Analysis - Tropical Region (30°S to 30°N)
Calculate precipitation using energy budget: L*P = R_toa - R_sfc - SH
where L = 2.5×10^6 J/kg (latent heat of vaporization)
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Physical constants
L_vap = 2.5e6  # Latent heat of vaporization (J/kg)
rho_water = 1000  # Density of water (kg/m³)

# Open file
file_path = "/data/anu_x1/ecmwf_data/era5_energy_budget_1990_2020/data_stream-moda_stepType-avgad.nc"
ds = xr.open_dataset(file_path)

# Restrict time to last 30 years (1991-2020)
ds = ds.sel(valid_time=slice("1991-01-01", "2020-12-31"))

# Restrict to tropical region (30°S to 30°N)
ds_tropics = ds.sel(latitude=slice(30, -30))  # Note: ERA5 latitude is decreasing (90 to -90)

print(f"Selected time period: {ds_tropics.valid_time.dt.year.min().values}-{ds_tropics.valid_time.dt.year.max().values}")
print(f"Number of months: {len(ds_tropics.valid_time)}")
print(f"Latitude range: {ds_tropics.latitude.max().values:.1f}°N to {ds_tropics.latitude.min().values:.1f}°S")
print(f"Number of latitude points: {len(ds_tropics.latitude)}")

# ERA5 monthly data is already averaged (stepType = "avgad")
# Data represents monthly averaged daily fluxes, already in W/m2 equivalent
# NO conversion needed - data is already in flux units
print("ERA5 monthly data (stepType = avgad): already averaged, no conversion needed")

# Use data directly without conversion
ds_flux = ds_tropics

# Extract variables with correct sign conventions based on metadata
ssr  = ds_flux["ssr"]    # surface net shortwave (downward positive)
str_var = ds_flux["str"] # surface net longwave (upward positive in CF)
tsr  = ds_flux["tsr"]    # TOA net shortwave (upward positive in CF)
ttr  = ds_flux["ttr"]    # TOA outgoing longwave (upward positive in CF)
sshf = ds_flux["sshf"]   # sensible heat flux (upward positive)
slhf = ds_flux["slhf"]   # latent heat flux (upward positive)
tisr = ds_flux["tisr"]   # TOA incident shortwave (downward positive)

# Also get actual precipitation for comparison
tp_actual = ds_tropics["tp"]  # Total precipitation (m of water equivalent)

# Net surface radiative flux (downward positive)
R_sfc = ssr - str_var

# Net TOA radiative flux (downward positive)
R_toa = tisr - tsr - ttr

# Calculate precipitation from energy budget
# L*P = R_toa - R_sfc - SH
# P = (R_toa - R_sfc - SH) / L
# where SH = sensible heat flux (sshf)

# Energy available for latent heat (W/m²)
energy_for_latent = R_toa - R_sfc - sshf

# Convert to precipitation rate
# For monthly averaged data: energy_for_latent is already in W/m² (average flux)
# P (kg/(s·m²)) = Energy (W/m²) / L (J/kg) = Energy (J/(s·m²)) / L (J/kg)
# P (m/s) = P (kg/(s·m²)) / rho_water (kg/m³)
precip_rate_ms = energy_for_latent / (L_vap * rho_water)  # m/s

# Convert to monthly precipitation (m/month)
# For monthly data: multiply by actual seconds in each month
days_in_month = ds_tropics.valid_time.dt.days_in_month
seconds_in_month = days_in_month * 24 * 3600
precip_monthly_m = precip_rate_ms * seconds_in_month

# Actual precipitation is already monthly accumulated (m/month)
tp_monthly_m = tp_actual

print(f"\nUsing latent heat of vaporization: L = {L_vap:.1e} J/kg")
print(f"Water density: ρ = {rho_water} kg/m³")

# Area-weighted means for tropics
weights = np.cos(np.deg2rad(ds_tropics["latitude"]))
weights.name = "weights"

# Calculate tropical means
R_toa_tmean = R_toa.weighted(weights).mean(("longitude", "latitude"))
R_sfc_tmean = R_sfc.weighted(weights).mean(("longitude", "latitude"))
sshf_tmean = sshf.weighted(weights).mean(("longitude", "latitude"))
slhf_tmean = slhf.weighted(weights).mean(("longitude", "latitude"))
energy_latent_tmean = energy_for_latent.weighted(weights).mean(("longitude", "latitude"))

# Precipitation means
precip_budget_tmean = precip_monthly_m.weighted(weights).mean(("longitude", "latitude"))
precip_actual_tmean = tp_monthly_m.weighted(weights).mean(("longitude", "latitude"))

# Time averages
R_toa_mean = float(R_toa_tmean.mean().values)
R_sfc_mean = float(R_sfc_tmean.mean().values)
sshf_mean = float(sshf_tmean.mean().values)
slhf_mean = float(slhf_tmean.mean().values)
energy_latent_mean = float(energy_latent_tmean.mean().values)
precip_budget_mean = float(precip_budget_tmean.mean().values)
precip_actual_mean = float(precip_actual_tmean.mean().values)

# Convert precipitation to mm/month for easier interpretation
precip_budget_mean_mm = precip_budget_mean * 1000
precip_actual_mean_mm = precip_actual_mean * 1000

print("="*70)
print("PRECIPITATION FROM ENERGY BUDGET - TROPICS (30°S-30°N, 1991-2020)")
print("="*70)
print("Energy budget components (time-averaged tropical means):")
print(f"  TOA net radiation (R_toa):     {R_toa_mean:8.2f} W/m²")
print(f"  Surface net radiation (R_sfc): {R_sfc_mean:8.2f} W/m²")
print(f"  Sensible heat flux (SH):       {sshf_mean:8.2f} W/m²")
print(f"  Latent heat flux (LH):         {slhf_mean:8.2f} W/m²")
print()
print("Energy budget calculation:")
print(f"  Energy for latent heat = R_toa - R_sfc - SH = {energy_latent_mean:8.2f} W/m²")
print(f"  Formula: L*P = {energy_latent_mean:8.2f} W/m²")
print()
print("Precipitation results:")
print(f"  From energy budget:     {precip_budget_mean_mm:8.1f} mm/month")
print(f"  From ERA5 data:         {precip_actual_mean_mm:8.1f} mm/month")
print(f"  Difference:             {precip_budget_mean_mm - precip_actual_mean_mm:8.1f} mm/month")
print(f"  Relative error:         {100*(precip_budget_mean_mm - precip_actual_mean_mm)/precip_actual_mean_mm:8.1f}%")

# Check consistency with latent heat flux
# LH should equal L * P (in W/m²)
implied_LH = energy_latent_mean
actual_LH = slhf_mean
print()
print("Consistency check:")
print(f"  Implied latent heat flux:  {implied_LH:8.2f} W/m²")
print(f"  Actual latent heat flux:   {actual_LH:8.2f} W/m²")
print(f"  Difference:                {implied_LH - actual_LH:8.2f} W/m²")

# Create plots
print("\nCreating precipitation comparison plots...")

# Convert time to years for plotting
years = np.arange(1991, 1991 + len(ds_tropics.valid_time)/12, 1/12)[:len(ds_tropics.valid_time)]

# Convert time series to mm/month
precip_budget_tmean_mm = precip_budget_tmean * 1000
precip_actual_tmean_mm = precip_actual_tmean * 1000

# Create plots
fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))

# Plot 1: Energy budget components
ax1.plot(years, R_toa_tmean, 'r-', linewidth=2, label='TOA net')
ax1.plot(years, R_sfc_tmean, 'b-', linewidth=2, label='Surface net')
ax1.plot(years, sshf_tmean, 'g-', linewidth=2, label='Sensible')
ax1.plot(years, energy_latent_tmean, 'purple', linewidth=2, label='Energy for latent')
ax1.set_xlabel('Year')
ax1.set_ylabel('Energy Flux (W/m²)')
ax1.set_title('Energy Budget Components - Tropics (30°S-30°N)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Precipitation comparison
ax2.plot(years, precip_budget_tmean_mm, 'r-', linewidth=2, label='From energy budget')
ax2.plot(years, precip_actual_tmean_mm, 'b-', linewidth=2, label='ERA5 actual')
ax2.set_xlabel('Year')
ax2.set_ylabel('Precipitation (mm/month)')
ax2.set_title('Precipitation: Energy Budget vs ERA5 Data')
ax2.legend()
ax2.grid(True, alpha=0.3)

# Plot 3: Precipitation difference
diff_mm = precip_budget_tmean_mm - precip_actual_tmean_mm
ax3.plot(years, diff_mm, 'k-', linewidth=2)
ax3.set_xlabel('Year')
ax3.set_ylabel('Precipitation Difference (mm/month)')
ax3.set_title('Precipitation Difference (Energy Budget - ERA5)')
ax3.grid(True, alpha=0.3)
ax3.axhline(y=0, color='r', linestyle='--', alpha=0.5)

plt.tight_layout()
plt.savefig('precipitation_energy_budget_tropics.png', dpi=300, bbox_inches='tight')
print("Plot saved to: precipitation_energy_budget_tropics.png")

# Calculate correlation
correlation = np.corrcoef(precip_budget_tmean_mm, precip_actual_tmean_mm)[0,1]
print(f"\nCorrelation between energy budget and ERA5 precipitation: {correlation:.3f}")

# Save results
results_data = {
    'years': years,
    'precip_budget_mm': precip_budget_tmean_mm.values,
    'precip_actual_mm': precip_actual_tmean_mm.values,
    'energy_components': {
        'R_toa': R_toa_tmean.values,
        'R_sfc': R_sfc_tmean.values,
        'sshf': sshf_tmean.values,
        'slhf': slhf_tmean.values,
        'energy_for_latent': energy_latent_tmean.values
    },
    'statistics': {
        'precip_budget_mean_mm': precip_budget_mean_mm,
        'precip_actual_mean_mm': precip_actual_mean_mm,
        'correlation': correlation,
        'relative_error_percent': 100*(precip_budget_mean_mm - precip_actual_mean_mm)/precip_actual_mean_mm
    }
}

import pickle
with open('precipitation_energy_budget_results.pkl', 'wb') as f:
    pickle.dump(results_data, f)
print("Results saved to: precipitation_energy_budget_results.pkl")

print("\n" + "="*70)
print("ANALYSIS SUMMARY")
print("="*70)
print("This analysis tests the energy budget approach to estimate precipitation")
print("using the formula: L*P = R_toa - R_sfc - SH")
print(f"The method {'agrees well' if abs(100*(precip_budget_mean_mm - precip_actual_mean_mm)/precip_actual_mean_mm) < 10 else 'shows significant differences'} with ERA5 precipitation data.")
print(f"Correlation: {correlation:.3f}")
print(f"Mean difference: {precip_budget_mean_mm - precip_actual_mean_mm:.1f} mm/month")
